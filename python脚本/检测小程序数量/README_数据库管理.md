# 微信账号数据库管理系统

## 概述

这个系统用于将微信账号数据从API获取并存储到SQLite数据库中，提供数据管理、搜索和统计功能。

## 文件说明

### 核心文件
- `database_manager.py` - 数据库管理核心类
- `使用数据库管理示例.py` - 使用示例和演示
- `获取所有微信账号.py` - 原始数据获取脚本

### 数据文件
- `weixin_accounts.db` - SQLite数据库文件（运行后自动生成）
- `weixin_accounts_export.json` - 导出的JSON数据文件

## 功能特性

### 1. 数据获取与存储
- 从API自动获取微信账号数据
- 自动去重和更新现有记录
- 支持增量同步

### 2. 数据库管理
- 自动创建数据库表结构
- 支持数据增删改查
- 记录同步日志

### 3. 搜索功能
- 按微信ID搜索
- 按昵称搜索
- 按手机号搜索
- 全字段模糊搜索

### 4. 统计功能
- 总账号数统计
- 活跃账号统计
- 手机号绑定率统计

### 5. 数据导出
- 导出为JSON格式
- 支持自定义文件名

## 使用方法

### 快速开始

1. **运行完整示例**
```bash
python 使用数据库管理示例.py
```

2. **直接使用数据库管理器**
```python
from database_manager import WeixinAccountDB

# 初始化
db = WeixinAccountDB()

# 获取并保存数据
json_data = db.fetch_weixin_accounts()
db.save_to_database(json_data)

# 查看统计
stats = db.get_account_stats()
print(f"总账号数: {stats['total_accounts']}")
```

### 详细使用示例

#### 1. 基本操作
```python
from database_manager import WeixinAccountDB

# 创建数据库管理器
db = WeixinAccountDB('my_accounts.db')

# 获取数据
data = db.fetch_weixin_accounts()
if data:
    db.save_to_database(data)
```

#### 2. 搜索功能
```python
# 按微信ID搜索
results = db.search_accounts("wxid_123", "wxid")

# 按昵称搜索
results = db.search_accounts("张三", "nickname")

# 全字段搜索
results = db.search_accounts("关键词", "all")
```

#### 3. 统计功能
```python
stats = db.get_account_stats()
print(f"总账号: {stats['total_accounts']}")
print(f"活跃账号: {stats['active_accounts']}")
print(f"绑定手机: {stats['mobile_accounts']}")
```

#### 4. 数据导出
```python
# 导出到JSON文件
db.export_to_json("my_export.json")
```

## 数据库结构

### weixin_accounts 表
| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | INTEGER | 主键，自增 |
| wxid | TEXT | 微信ID（唯一） |
| nickname | TEXT | 微信昵称 |
| alias | TEXT | 微信别名 |
| uin | TEXT | 用户唯一标识 |
| username | TEXT | 用户名 |
| bind_mobile | TEXT | 绑定手机号 |
| status | TEXT | 账号状态 |
| create_time | TEXT | 创建时间 |
| update_time | TEXT | 更新时间 |

### sync_logs 表
| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | INTEGER | 主键，自增 |
| sync_time | TEXT | 同步时间 |
| total_count | INTEGER | 总数据量 |
| new_count | INTEGER | 新增数量 |
| update_count | INTEGER | 更新数量 |
| status | TEXT | 同步状态 |

## 注意事项

1. **API依赖**: 需要有效的API Token和网络连接
2. **数据格式**: 确保API返回的数据格式正确
3. **数据库文件**: 数据库文件会在首次运行时自动创建
4. **并发安全**: 支持多线程安全操作
5. **错误处理**: 内置异常处理和回滚机制

## 错误排查

### 常见问题

1. **API连接失败**
   - 检查网络连接
   - 验证Token是否有效
   - 确认API地址正确

2. **数据库错误**
   - 检查文件权限
   - 确认磁盘空间充足
   - 验证数据库文件完整性

3. **数据格式错误**
   - 检查API返回的数据结构
   - 确认JSON格式正确

## 扩展功能

可以根据需要扩展以下功能：
- 支持MySQL/PostgreSQL数据库
- 添加数据可视化功能
- 实现定时同步功能
- 添加数据备份功能
- 支持更多搜索条件

## 许可证

本项目仅供学习和研究使用。 