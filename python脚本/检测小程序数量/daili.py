import requests
import time

# API链接    后台获取链接地址
proxyAPI = "http://he5360726.user.xiecaiyun.com/api/proxies?action=getText&key=NPCFF68568&count=&word=&rand=false&norepeat=false&detail=false&ltime=&idshow=false"
proxyusernm = "he5360726"        # 代理帐号
proxypasswd = "he5360726"       # 代理密码
url = 'https://myip.ipip.net/'

# 获取代理IP
r = requests.get(proxyAPI)
proxy_ip = r.text.strip()
if proxy_ip:
    proxyurl = f"http://{proxyusernm}:{proxypasswd}@{proxy_ip}"
    t1 = time.time()
    try:
        r = requests.get(url, proxies={'http': proxyurl, 'https': proxyurl}, headers={
            "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8",
            "Accept-Encoding": "gzip, deflate",
            "Accept-Language": "zh-CN,zh;q=0.9",
            "Cache-Control": "max-age=0",
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/71.0.3578.98 Safari/537.36"
        }, timeout=15)
        r.encoding = 'utf-8'
        t2 = time.time()
        print(r.text)
        print("时间差:", (t2 - t1))
    except Exception as e:
        print(f"代理访问失败: {e}")
else:
    print('未获取到代理IP')
