import requests
import json
from database_manager import WeixinAccountDB

auth_headers = {
    'Authorization': 'your_secret_token_123'
}
auth_response = requests.get('http://***************:5000/get/gzh', headers=auth_headers)
authorization = auth_response.text
def save_hook_data():
    """
    @description 发送请求并将响应内容保存到数据库中
    """
    url = 'https://web.raisedsun.com/v1-api/weixin-account/list-data'
    
    headers = {
        'Accept': 'application/json, text/plain, */*',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6',
        'Connection': 'keep-alive',
        'Origin': 'https://web.raisedsun.com',
        'Sec-Fetch-Dest': 'empty',
        'Sec-Fetch-Mode': 'cors',
        'Sec-Fetch-Site': 'same-origin',
        'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
        'X-Token': authorization,
        'sec-ch-ua': '"Chromium";v="134", "Not:A-Brand";v="24", "Microsoft Edge";v="134"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"macOS"'
    }
    
    cookies = {
        'Admin-Token': authorization,
        'sidebarStatus': '1'
    }
    
    params = {
        'pageNo': '1',
        'pageSize': '9093'
    }
    
    try:
        print("正在从API获取微信账号数据...")
        response = requests.post(url, headers=headers, cookies=cookies, params=params)
        response.raise_for_status()
        
        # 解析JSON响应
        json_data = response.json()
        
        # 初始化数据库管理器
        db = WeixinAccountDB()
        
        # 保存到数据库
        print("正在保存数据到数据库...")
        success = db.save_to_database(json_data)
        
        if success:
            print("✓ 数据已成功保存到数据库")
            
            # 显示统计信息
            stats = db.get_account_stats()
            print(f"\n数据库统计信息:")
            print(f"- 总账号数: {stats['total_accounts']}")
            print(f"- 活跃账号数: {stats['active_accounts']}")
            print(f"- 绑定手机号账号数: {stats['mobile_accounts']}")
            print(f"- 有62数据的账号数: {stats['has62_accounts']}")
            print(f"- Hook账号数: {stats['hook_accounts']}")
            
            # 可选：同时保存到JSON文件作为备份
            print("\n正在保存备份到JSON文件...")
            with open('1.json', 'w', encoding='utf-8') as f:
                json.dump(json_data, f, ensure_ascii=False, indent=4)
            print("✓ 备份已保存到1.json文件")
            
        else:
            print("✗ 数据保存到数据库失败")
            
    except requests.exceptions.RequestException as e:
        print(f"请求失败: {e}")
    except json.JSONDecodeError as e:
        print(f"JSON解析失败: {e}")
    except Exception as e:
        print(f"保存数据时发生错误: {e}")

if __name__ == '__main__':
    save_hook_data()