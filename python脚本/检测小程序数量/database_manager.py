import sqlite3
import json
import requests
from datetime import datetime
import os

class WeixinAccountDB:
    def __init__(self, db_path='weixin_accounts.db'):
        """
        初始化数据库连接
        @param db_path: 数据库文件路径
        """
        self.db_path = db_path
        self.init_database()
    
    def init_database(self):
        """
        初始化数据库表结构
        """
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # 创建微信账号表 - 使用API响应的原始字段名
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS weixin_accounts (
                id TEXT PRIMARY KEY,
                weixinId TEXT UNIQUE,
                mobile TEXT,
                password TEXT,
                data62 TEXT,
                nickname TEXT,
                alias TEXT,
                avatar TEXT,
                remark TEXT,
                accountStatus INTEGER,
                accountStatusNot TEXT,
                loginTime INTEGER,
                firstLoginTime INTEGER,
                loginResult TEXT,
                createTime INTEGER,
                createBy TEXT,
                qrUsername TEXT,
                extInfo1 TEXT,
                extInfo2 TEXT,
                extInfo3 TEXT,
                hookMachineTag TEXT,
                pcProtoMachineTag TEXT,
                shouhaoFlag INTEGER,
                force62Flag INTEGER,
                hkFlag INTEGER,
                pcProtoFlag TEXT,
                freezeFlag TEXT,
                freezeTime TEXT,
                lastHookOnlineTime INTEGER,
                xzProtoLoginTime INTEGER,
                xzProtoLastActiveTime INTEGER,
                regCountry TEXT,
                has62 BOOLEAN,
                isOnline TEXT,
                lastHookOnlineTimeGte TEXT,
                lastHookOnlineTimeLte TEXT,
                hook BOOLEAN,
                username TEXT,
                create_time_db TEXT,
                update_time TEXT
            )
        ''')
        
        # 创建数据同步记录表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS sync_logs (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                sync_time TEXT,
                total_count INTEGER,
                new_count INTEGER,
                update_count INTEGER,
                status TEXT
            )
        ''')
        
        conn.commit()
        conn.close()
        print(f"数据库初始化完成: {self.db_path}")
    
    def fetch_weixin_accounts(self):
        """
        从API获取微信账号数据
        """
        url = 'https://web.raisedsun.com/v1-api/weixin-account/list-data'
        
        headers = {
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6',
            'Connection': 'keep-alive',
            'Origin': 'https://web.raisedsun.com',
            'Sec-Fetch-Dest': 'empty',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Site': 'same-origin',
            'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/134.0.0.0 Safari/537.36 Edg/134.0.0.0',
            'X-Token': '9d980e2c24c64567a3925a9ca9e13f28',
            'sec-ch-ua': '"Chromium";v="134", "Not:A-Brand";v="24", "Microsoft Edge";v="134"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"macOS"'
        }
        
        cookies = {
            'Admin-Token': '9d980e2c24c64567a3925a9ca9e13f28',
            'sidebarStatus': '1'
        }
        
        params = {
            'pageNo': '1',
            'pageSize': '9093'
        }
        
        try:
            response = requests.post(url, headers=headers, cookies=cookies, params=params)
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            print(f"获取数据失败: {e}")
            return None
    
    def save_to_database(self, json_data):
        """
        将JSON数据保存到数据库
        @param json_data: API返回的JSON数据
        """
        if not json_data or 'list' not in json_data:
            print("无效的数据格式")
            return False
        
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        total_count = 0
        new_count = 0
        update_count = 0
        
        try:
            # 处理数据列表
            accounts = json_data['list']
            total_count = len(accounts)
            
            for account in accounts:
                # 直接使用API响应的原始字段名，不做任何修改
                id = account.get('id', '')
                weixinId = account.get('weixinId', '')
                mobile = account.get('mobile', '')
                password = account.get('password', '')
                data62 = account.get('data62', '')
                nickname = account.get('nickname', '')
                alias = account.get('alias', '')
                avatar = account.get('avatar', '')
                remark = account.get('remark', '')
                accountStatus = account.get('accountStatus', '')
                accountStatusNot = account.get('accountStatusNot', '')
                loginTime = account.get('loginTime', '')
                firstLoginTime = account.get('firstLoginTime', '')
                loginResult = account.get('loginResult', '')
                createTime = account.get('createTime', '')
                createBy = account.get('createBy', '')
                qrUsername = account.get('qrUsername', '')
                extInfo1 = account.get('extInfo1', '')
                extInfo2 = account.get('extInfo2', '')
                extInfo3 = account.get('extInfo3', '')
                hookMachineTag = account.get('hookMachineTag', '')
                pcProtoMachineTag = account.get('pcProtoMachineTag', '')
                shouhaoFlag = account.get('shouhaoFlag', '')
                force62Flag = account.get('force62Flag', '')
                hkFlag = account.get('hkFlag', '')
                pcProtoFlag = account.get('pcProtoFlag', '')
                freezeFlag = account.get('freezeFlag', '')
                freezeTime = account.get('freezeTime', '')
                lastHookOnlineTime = account.get('lastHookOnlineTime', '')
                xzProtoLoginTime = account.get('xzProtoLoginTime', '')
                xzProtoLastActiveTime = account.get('xzProtoLastActiveTime', '')
                regCountry = account.get('regCountry', '')
                has62 = account.get('has62', False)
                isOnline = account.get('isOnline', '')
                lastHookOnlineTimeGte = account.get('lastHookOnlineTimeGte', '')
                lastHookOnlineTimeLte = account.get('lastHookOnlineTimeLte', '')
                hook = account.get('hook', False)
                username = account.get('username', '')
                
                # 检查是否已存在
                cursor.execute('SELECT id FROM weixin_accounts WHERE weixinId = ?', (weixinId,))
                existing = cursor.fetchone()
                
                if existing:
                    # 更新现有记录 - 使用原始字段名
                    cursor.execute('''
                        UPDATE weixin_accounts 
                        SET mobile=?, password=?, data62=?, nickname=?, alias=?, avatar=?, remark=?, 
                            accountStatus=?, accountStatusNot=?, loginTime=?, firstLoginTime=?, loginResult=?, 
                            createTime=?, createBy=?, qrUsername=?, extInfo1=?, extInfo2=?, extInfo3=?, 
                            hookMachineTag=?, pcProtoMachineTag=?, shouhaoFlag=?, force62Flag=?, hkFlag=?, 
                            pcProtoFlag=?, freezeFlag=?, freezeTime=?, lastHookOnlineTime=?, xzProtoLoginTime=?, 
                            xzProtoLastActiveTime=?, regCountry=?, has62=?, isOnline=?, lastHookOnlineTimeGte=?, 
                            lastHookOnlineTimeLte=?, hook=?, username=?, update_time=?
                        WHERE weixinId=?
                    ''', (mobile, password, data62, nickname, alias, avatar, remark, accountStatus, 
                         accountStatusNot, loginTime, firstLoginTime, loginResult, createTime, createBy, 
                         qrUsername, extInfo1, extInfo2, extInfo3, hookMachineTag, pcProtoMachineTag, 
                         shouhaoFlag, force62Flag, hkFlag, pcProtoFlag, freezeFlag, freezeTime, 
                         lastHookOnlineTime, xzProtoLoginTime, xzProtoLastActiveTime, regCountry, has62, 
                         isOnline, lastHookOnlineTimeGte, lastHookOnlineTimeLte, hook, username, current_time, weixinId))
                    update_count += 1
                else:
                    # 插入新记录 - 使用原始字段名
                    cursor.execute('''
                        INSERT INTO weixin_accounts 
                        (id, weixinId, mobile, password, data62, nickname, alias, avatar, remark, 
                         accountStatus, accountStatusNot, loginTime, firstLoginTime, loginResult, createTime, 
                         createBy, qrUsername, extInfo1, extInfo2, extInfo3, hookMachineTag, pcProtoMachineTag, 
                         shouhaoFlag, force62Flag, hkFlag, pcProtoFlag, freezeFlag, freezeTime, lastHookOnlineTime, 
                         xzProtoLoginTime, xzProtoLastActiveTime, regCountry, has62, isOnline, lastHookOnlineTimeGte, 
                         lastHookOnlineTimeLte, hook, username, create_time_db, update_time)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    ''', (id, weixinId, mobile, password, data62, nickname, alias, avatar, remark, accountStatus,
                         accountStatusNot, loginTime, firstLoginTime, loginResult, createTime, createBy, qrUsername,
                         extInfo1, extInfo2, extInfo3, hookMachineTag, pcProtoMachineTag, shouhaoFlag, force62Flag,
                         hkFlag, pcProtoFlag, freezeFlag, freezeTime, lastHookOnlineTime, xzProtoLoginTime,
                         xzProtoLastActiveTime, regCountry, has62, isOnline, lastHookOnlineTimeGte, lastHookOnlineTimeLte,
                         hook, username, current_time, current_time))
                    new_count += 1
            
            # 记录同步日志
            cursor.execute('''
                INSERT INTO sync_logs (sync_time, total_count, new_count, update_count, status)
                VALUES (?, ?, ?, ?, ?)
            ''', (current_time, total_count, new_count, update_count, 'success'))
            
            conn.commit()
            print(f"数据同步完成: 总数={total_count}, 新增={new_count}, 更新={update_count}")
            return True
            
        except Exception as e:
            conn.rollback()
            print(f"保存数据失败: {e}")
            return False
        finally:
            conn.close()
    
    def get_account_stats(self):
        """
        获取账号统计信息
        """
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('SELECT COUNT(*) FROM weixin_accounts')
        total_accounts = cursor.fetchone()[0]
        
        cursor.execute('SELECT COUNT(*) FROM weixin_accounts WHERE accountStatus = 2')
        active_accounts = cursor.fetchone()[0]
        
        cursor.execute('SELECT COUNT(*) FROM weixin_accounts WHERE mobile IS NOT NULL AND mobile != ""')
        mobile_accounts = cursor.fetchone()[0]
        
        cursor.execute('SELECT COUNT(*) FROM weixin_accounts WHERE has62 = 1')
        has62_accounts = cursor.fetchone()[0]
        
        cursor.execute('SELECT COUNT(*) FROM weixin_accounts WHERE hook = 1')
        hook_accounts = cursor.fetchone()[0]
        
        conn.close()
        
        return {
            'total_accounts': total_accounts,
            'active_accounts': active_accounts,
            'mobile_accounts': mobile_accounts,
            'has62_accounts': has62_accounts,
            'hook_accounts': hook_accounts
        }
    
    def search_accounts(self, keyword, search_type='wxid'):
        """
        搜索账号
        @param keyword: 搜索关键词
        @param search_type: 搜索类型 (wxid, nickname, mobile, remark)
        """
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        if search_type == 'wxid':
            cursor.execute('SELECT * FROM weixin_accounts WHERE weixinId LIKE ?', (f'%{keyword}%',))
        elif search_type == 'nickname':
            cursor.execute('SELECT * FROM weixin_accounts WHERE nickname LIKE ?', (f'%{keyword}%',))
        elif search_type == 'mobile':
            cursor.execute('SELECT * FROM weixin_accounts WHERE mobile LIKE ?', (f'%{keyword}%',))
        elif search_type == 'remark':
            cursor.execute('SELECT * FROM weixin_accounts WHERE remark LIKE ?', (f'%{keyword}%',))
        else:
            cursor.execute('SELECT * FROM weixin_accounts WHERE weixinId LIKE ? OR nickname LIKE ? OR mobile LIKE ? OR remark LIKE ?', 
                         (f'%{keyword}%', f'%{keyword}%', f'%{keyword}%', f'%{keyword}%'))
        
        results = cursor.fetchall()
        conn.close()
        
        return results
    
    def export_to_json(self, filename='exported_accounts.json'):
        """
        导出数据到JSON文件
        """
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('SELECT * FROM weixin_accounts')
        rows = cursor.fetchall()
        
        # 获取列名
        columns = [description[0] for description in cursor.description]
        
        # 转换为字典列表
        data = []
        for row in rows:
            data.append(dict(zip(columns, row)))
        
        conn.close()
        
        # 保存到JSON文件
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=4)
        
        print(f"数据已导出到: {filename}")
        return data
    
    def add_columns(self, columns_dict):
        """
        向数据库表添加新字段
        @param columns_dict: 字段字典，格式为 {'字段名': '字段类型'}
        """
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        added_count = 0
        existing_count = 0
        
        for column_name, column_type in columns_dict.items():
            try:
                cursor.execute(f'ALTER TABLE weixin_accounts ADD COLUMN {column_name} {column_type}')
                print(f"✓ 添加字段: {column_name}")
                added_count += 1
            except Exception as e:
                if "duplicate column name" in str(e).lower():
                    print(f"字段 {column_name} 已存在")
                    existing_count += 1
                else:
                    print(f"添加字段 {column_name} 时出错: {e}")
        
        conn.commit()
        conn.close()
        
        print(f"字段添加完成: 新增 {added_count} 个字段, {existing_count} 个字段已存在")
        return added_count
    
    def update_search_results(self, weixinId, bindMobile, biz_admin_count, wxa_admin_count, bank_count, search_status="success"):
        """
        更新搜索结果的字段
        @param weixinId: 微信ID
        @param bindMobile: 绑定手机号
        @param biz_admin_count: 公众号管理员数量
        @param wxa_admin_count: 小程序管理员数量
        @param bank_count: 银行卡数量
        @param search_status: 搜索状态
        """
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        
        try:
            cursor.execute('''
                UPDATE weixin_accounts 
                SET bindMobile=?, biz_admin_count=?, wxa_admin_count=?, bank_count=?, search_time=?, search_status=?
                WHERE weixinId=?
            ''', (bindMobile, biz_admin_count, wxa_admin_count, bank_count, current_time, search_status, weixinId))
            
            conn.commit()
            return True
        except Exception as e:
            print(f"更新搜索结果失败: {e}")
            return False
        finally:
            conn.close()

def main():
    """
    主函数 - 演示数据库操作
    """
    db = WeixinAccountDB()
    
    print("=== 微信账号数据库管理系统 ===")
    
    # 获取数据并保存到数据库
    print("\n1. 正在从API获取数据...")
    json_data = db.fetch_weixin_accounts()
    
    if json_data:
        print("2. 正在保存数据到数据库...")
        success = db.save_to_database(json_data)
        
        if success:
            print("3. 获取统计信息...")
            stats = db.get_account_stats()
            print(f"总账号数: {stats['total_accounts']}")
            print(f"活跃账号数: {stats['active_accounts']}")
            print(f"绑定手机号账号数: {stats['mobile_accounts']}")
            
            # 导出数据
            print("\n4. 导出数据到JSON文件...")
            db.export_to_json()
        else:
            print("数据保存失败")
    else:
        print("获取数据失败")

if __name__ == '__main__':
    main() 